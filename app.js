import path from "path";
import AutoLoad from "@fastify/autoload";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

import xConfig from "@xenterprises/fastify-xconfig";

export const options = {};

export default async function (fastify, opts) {
  // Register XConfig with all options, including Twilio, SendGrid, and Stripe
  fastify.register(xConfig, {
    professional: false,
    fancyErrors: true,
    prisma: {},
    bugsnag: {
      apiKey: process.env.BUGSNAG_API_KEY,
    },
    rateLimit: {
      max: process.env.RATE_LIMIT_MAX || 100,
      timeWindow: process.env.RATE_LIMIT_TIME_WINDOW || "1 minute",
    },
    stripe: {
      active: false,
    },
    sendGrid: {
      active: false,
    },
    twilio: {
      active: false,
    },
    cloudinary: {
      active: true,
      cloudName: process.env.CLOUDINARY_CLOUD_NAME,
      apiKey: process.env.CLOUDINARY_API_KEY,
      apiSecret: process.env.CLOUDINARY_API_SECRET,
      folder: process.env.CLOUDINARY_FOLDER || "general",
    },
    multipart: {
      active: true,
      limits: {
        fileSize: 50 * 1024 * 1024, // 50MB file size limit
        fieldSize: 50 * 1024 * 1024, // 50MB field size limit
        files: 10,
        fields: 10,
        headerPairs: 2000, // Increase header pairs limit
      },
      throwFileSizeLimit: false, // Don't throw on file size limit, handle gracefully
      attachFieldsToBody: true,
    },
    cors: {
      active: true,
      origin: [
        "http://localhost:3000",
        "https://case-admin-nuxt.vercel.app",
        "https://api-shawnigan.up.railway.app",
      ],
      credentials: true,
    },
    auth: {
      excludedPaths: ["/public", "/portal/auth/register"],
      admin: {
        active: true,
        stackProjectId: process.env.ADMIN_STACK_PROJECT_ID,
      },
      user: {
        active: true,
        portalStackProjectId: process.env.STACK_PROJECT_ID,
        me: {
          isOnboarded: true,
        },
        // registerEmail: {
        //   subject: "Welcome to Bandmate!",
        //   templateId: "",
        // },
      },
    },
    geocode: {
      active: false,
      apiKey: process.env.GEOCODIO_API_KEY,
    },
  });

  // This loads all plugins defined in plugins, excluding specific ones
  fastify.register(AutoLoad, {
    dir: path.join(__dirname, "plugins"),
    options: Object.assign({}, opts),
    ignorePattern: /xConfig\.js$/,
  });

  // This loads all plugins defined in routes
  fastify.register(AutoLoad, {
    dir: path.join(__dirname, "routes"),
    options: Object.assign({}, opts),
    dirNameRoutePrefix: true,
  });
}
