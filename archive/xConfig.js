/**
 * *xConfig - Fastify configuration plugin for setting up middleware, services, and route handling.*
 *
 * This plugin provides a comprehensive setup for configuring various services such as Prisma, SendGrid,
 * Twilio, Cloudinary, CORS, rate limiting, authentication, and more within a Fastify instance.
 * It centralizes configuration, allowing for easy customization and scaling.
 *
 * *Key Features:*
 * - Handles CORS, rate-limiting, multipart handling, and error handling out of the box.
 * - Integrates with third-party services such as SendGrid, Twilio, Cloudinary, Stripe, and Bugsnag.
 * - Provides authentication setup for both Admin and User, with JWT-based token handling.
 * - Includes Prisma database connection and gracefully handles disconnecting on server shutdown.
 * - Adds health check routes and resource usage monitoring, with environment validation.
 * - Customizes routes listing and applies various performance and security options.
 *
 * *Usage:*
 * This plugin should be registered in your Fastify instance with custom options provided
 * for each service. It ensures proper initialization of services like SendGrid, Twilio, and Prisma.
 *
 * *Example:*
 * ```typescript
 * import Fastify from 'fastify';
 * import xConfig from './path/to/xConfig';
 *
 * const fastify = Fastify();
 * fastify.register(xConfig, {
 *   prisma: { active: true },
 *   sendGrid: { apiKey: 'your-sendgrid-api-key' },
 *   twilio: { accountSid: 'your-account-sid', authToken: 'your-auth-token', phoneNumber: 'your-twilio-number' },
 *   cloudinary: { cloudName: 'your-cloud-name', apiKey: 'your-api-key', apiSecret: 'your-api-secret' },
 *   auth: {
 *     admin: { secret: 'admin-jwt-secret', expiresIn: '1h' },
 *     user: { secret: 'user-jwt-secret', expiresIn: '1h' },
 *   },
 *   cors: { origin: ['https://your-frontend.com'], credentials: true },
 * });
 *
 * fastify.listen({ port: 3000 });
 * ```
 *
 * *Parameters:*
 * @param {Object} options - The options for configuring various plugins and services.
 * - prisma: Prisma Client configuration (optional).
 * - sendGrid: SendGrid configuration for email services (optional).
 * - twilio: Twilio configuration for SMS services (optional).
 * - cloudinary: Cloudinary configuration for media upload services (optional).
 * - auth: Authentication configuration for Admin and User JWT tokens (optional).
 * - cors: CORS configuration (optional).
 * - rateLimit: Rate-limiting options (optional).
 * - multipart: Multipart handling options for file uploads (optional).
 * - bugsnag: Bugsnag error reporting configuration (optional).
 * - underPressure: Under Pressure plugin options for monitoring and throttling under load (optional).
 *
 * *Authentication:*
 * The plugin provides both Admin and User authentication with JWT support. It sets secure cookies,
 * manages token refresh, and validates tokens on protected routes. For example:
 * - `/admin/auth/login` for admin login.
 * - `/portal/auth/login` for user login.
 * - `/admin/auth/me` to check authentication status for admins.
 * - `/portal/auth/me` to check authentication status for users.
 *
 * *Health Check:*
 * The `/health` route provides a health check with details about uptime, memory usage, CPU load,
 * database and Redis status, and environment variable validation.
 *
 * *Services:*
 * - **Prisma**: Initializes Prisma Client and decorates the Fastify instance for database queries.
 * - **SendGrid**: Provides an email sending service through SendGrid, integrated with the Fastify instance.
 * - **Twilio**: Provides SMS sending and phone number validation services.
 * - **Cloudinary**: Handles file uploads to Cloudinary and deletion of media files.
 * - **Authentication**: JWT-based authentication for Admin and User with token-based sessions.
 *
 * *Hooks:*
 * - `onRequest`: Validates JWT tokens for protected routes.
 * - `onClose`: Gracefully disconnects Prisma and other services on server shutdown.
 *
 * *Error Handling:*
 * Fancy error handling is enabled by default, showing enhanced error messages during development.
 * Bugsnag integration is optional for real-time error reporting.
 *
 * *Route Listing:*
 * The plugin prints all routes after registration, color-coded by HTTP method, unless disabled.
 *
 * *Dependencies:*
 * - Prisma Client, SendGrid, Twilio, Cloudinary, Fastify CORS, Fastify Rate Limit, Fastify Multipart,
 * Fastify Under Pressure, and Fastify Bugsnag are used for various services and integrations.
 *
 * *Notes:*
 * - Environment variables such as `DATABASE_URL`, `ADMIN_JWT_SECRET`, and `USER_JWT_SECRET` are expected to be set.
 * - Services like SendGrid, Twilio, and Cloudinary require API keys to be passed via the options.
 * - This plugin is highly customizable, with options to enable/disable each feature.
 *
 * *Example Configuration:*
 * ```javascript
 * fastify.register(xConfig, {
 *   prisma: { active: true },
 *   sendGrid: { apiKey: 'SG.your_api_key' },
 *   twilio: {
 *     accountSid: 'ACxxxxxxxxxxxxxxxx',
 *     authToken: 'your_auth_token',
 *     phoneNumber: '+**********',
 *   },
 *   cloudinary: {
 *     cloudName: 'your-cloud-name',
 *     apiKey: 'your-api-key',
 *     apiSecret: 'your-api-secret',
 *   },
 *   auth: {
 *     admin: {
 *       secret: 'admin-secret',
 *       expiresIn: '1h',
 *       cookieOptions: { name: 'adminCookie', refreshTokenName: 'adminRefreshToken' },
 *     },
 *     user: {
 *       secret: 'user-secret',
 *       expiresIn: '1h',
 *       cookieOptions: { name: 'userCookie', refreshTokenName: 'userRefreshToken' },
 *     },
 *   },
 *   cors: { origin: ['https://your-frontend.com'], credentials: true },
 * });
 * ```
 */

import fp from "fastify-plugin";
import jwt from "@fastify/jwt";
import bcrypt from "bcrypt";
import { PrismaClient } from "@prisma/client";
import Sendgrid from "@sendgrid/mail";
import Twilio from "twilio";
import { v2 as Cloudinary } from "cloudinary";
import { randomUUID } from "uncrypto"; // Import randomString from uncrypto
const isProduction = process.env.NODE_ENV === 'production';
/*
  ===== SET VARS =====
  Setting variables for the config
*/
const COLORS = { POST: 33, GET: 32, PUT: 34, DELETE: 31, PATCH: 90, clear: 39 };
const colorize = (m, t) =>
  `\u001b[${COLORS[m] || COLORS.clear}m${t}\u001b[${COLORS.clear}m`;
const printRoutes = (routes, colors = true) =>
  routes
    .sort((a, b) => a.url.localeCompare(b.url))
    .forEach(({ method, url }) => {
      // Ensure method is always an array
      const methodsArray = Array.isArray(method) ? method : [method];

      // Filter out 'HEAD' methods
      methodsArray
        .filter((m) => m !== 'HEAD')
        .forEach((m) =>
          console.info(
            `${colors ? colorize(m, m) : m}\t${colors ? colorize(m, url) : url}`
          )
        );
    });

// Import necessary modules
import os from "os";
import process from "process";
import fs from "fs";
import util from "util";

// Promisify fs functions
const statAsync = util.promisify(fs.stat);

// Record the server start time
const serverStartTime = Date.now();

// Helper function to format bytes into human-readable format
function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  const formattedNumber = parseFloat((bytes / Math.pow(k, i)).toFixed(dm));
  return `${formattedNumber} ${sizes[i]}`;
}

/*
  ===== SET MAIN FUNCTION AND OPTIONS =====
  Setting variables for the config
*/
async function xConfig(fastify, options) {
  const {
    professional = false,
    fancyErrors = true,
    prisma: prismaOptions = {},
    bugsnag: bugsnagOptions = {},
    stripe: stripeOptions = {},
    sendGrid: sendGridOptions = {},
    twilio: twilioOptions = {},
    cloudinary: cloudinaryOptions = {},
    auth: authOptions = {},
    cors: corsOptions = {},
    underPressure: underPressureOptions = {},
    multipart: multipartOptions = {},
    rateLimit: rateLimitOptions = {},
  } = options;

  // add starting console with emoji
  console.info("\n 🌮 Starting xConfig...\n");

  /*
    ===== LIST ROUTES =====
    Moved the onRoute hook to the top to capture all routes.
  */
  const routes = [];
  fastify.addHook("onRoute", (r) => routes.push(r));

  /*
    ===== CORS =====
  */
  if (corsOptions.active !== false) {

    await fastify.register(import("@fastify/cors"), {
      origin: corsOptions.origin || [
        "https://getx.io",
        "http://localhost:3000",
      ],
      credentials:
        corsOptions.credentials !== undefined ? corsOptions.credentials : true,
      methods: corsOptions.methods || [
        "GET",
        "POST",
        "PUT",
        "DELETE",
        "OPTIONS",
      ],
      strictPreflight: false,
      hideOptionsRoute: true,
      allowedHeaders: [
        "Content-Type",        // Standard content-type headers
        "Authorization",       // Needed for JWT or OAuth authentication
        "Accept",              // Accept headers for specifying response type
        "DNT",                 // Do Not Track header
        "Referer"             // Referrer header for tracking
      ],
      exposedHeaders: [
        "Authorization",       // For accessing token headers after login
        "Set-Cookie"           // Expose Set-Cookie header to client
      ],
    });
    console.info("   ✅ CORS Enabled");
  }

  /*
  ===== SENSIBLE =====
  */
  fastify.register(import('@fastify/sensible'))

  /*
    ===== UNDER PRESSURE =====
  */
  if (underPressureOptions.active !== false) {
    fastify.register(import("@fastify/under-pressure"), underPressureOptions);
    console.info("   ✅ Under Pressure Enabled");
  }

  /*
    ===== RATE LIMIT =====
  */
  if (rateLimitOptions.active !== false) {
    fastify.register(import("@fastify/rate-limit"), rateLimitOptions);
    console.info("   ✅ Rate Limiting Enabled");
  }

  /*
    ===== MULTIPART =====
  */
  if (multipartOptions.active !== false) {
    fastify.register(import("@fastify/multipart"), multipartOptions);
    console.info("   ✅ Multipart Enabled");
  }

  /*
    ===== BUGSNAG =====
  */
  if (bugsnagOptions.active !== false) {
    if (!bugsnagOptions.apiKey)
      throw new Error("Bugsnag API key must be provided.");
    fastify.register(import("fastify-bugsnag"), {
      apiKey: bugsnagOptions.apiKey,
    });
    console.info("   ✅ BugSnag Enabled");
  }

  /*
    ===== EXTEND ERRORS =====
  */
  if (fancyErrors !== false) {
    fastify.setErrorHandler((error, request, reply) => {
      const statusCode = error.statusCode || 500;
      const response = {
        status: statusCode,
        message: error.message || "Internal Server Error",
        // Only show stack in development mode
        stack: process.env.NODE_ENV === "development" ? error.stack : undefined,
      };

      // Optional Bugsnag error reporting
      if (fastify.bugsnag) {
        fastify.bugsnag.notify(error);
      }

      fastify.log.error(response);
      reply.status(statusCode).send(response);
    });
    console.info("   ✅ Fancy Errors Enabled");
  }

  /*
    ===== PRISMA =====
  */
  if (prismaOptions.active !== false) {
    delete prismaOptions.active;
    const prisma = new PrismaClient(prismaOptions);

    // Connect to the database
    await prisma.$connect();

    // Decorate Fastify instance with Prisma Client
    fastify.decorate("prisma", prisma);

    // Disconnect Prisma Client when Fastify closes
    fastify.addHook("onClose", async () => {
      await fastify.prisma.$disconnect();
    });

    console.info("   ✅ Prisma Enabled");
  }

  /*
    ===== STRIPE =====
  */
  if (stripeOptions.active === true) {
    if (!stripeOptions.apiKey)
      throw new Error("Stripe API key must be provided.");
    fastify.register(import("fastify-stripe"), {
      apiKey: stripeOptions.apiKey,
    });

    console.info("   ✅ Stripe Enabled");

    // TODO - Add decorators for Stripe if needed
  }

  /*
    ===== SENDGRID =====
  */
  if (sendGridOptions.active !== false) {
    if (!sendGridOptions.apiKey)
      throw new Error("SendGrid API key must be provided.");

    Sendgrid.setApiKey(sendGridOptions.apiKey);
    fastify.decorate("sendGrid", Sendgrid);
    fastify.decorate(
      "sendEmail",
      async (to, from, subject, templateId, dynamicTemplateData) => {
        try {
          const msg = {
            to,
            from,
            subject,
            templateId,
            dynamicTemplateData,
          };
          await Sendgrid.send(msg);
          return true;
        } catch (error) {
          fastify.log.error("SendGrid send email failed:", error);
          throw new Error("Failed to send email.");
        }
      }
    );

    console.info("   ✅ SendGrid Enabled");
  }

  /*
    ===== TWILIO =====
  */
  if (twilioOptions.active !== false) {
    // Return if missing Twilio credentials
    if (
      !twilioOptions.accountSid ||
      !twilioOptions.authToken ||
      !twilioOptions.phoneNumber
    )
      throw new Error(
        "Twilio accountSid, authToken, and phoneNumber must be provided."
      );

    const twilioClient = Twilio(
      twilioOptions.accountSid,
      twilioOptions.authToken
    );
    // Decorator to send SMS
    fastify.decorate("sendSMS", async (to, body) => {
      try {
        const message = await twilioClient.messages.create({
          body,
          to,
          from: twilioOptions.phoneNumber,
        });
        return message;
      } catch (error) {
        fastify.log.error("Twilio send SMS failed:", error);
        throw new Error("Failed to send SMS.");
      }
    });

    // Decorator to validate phone numbers
    fastify.decorate("validatePhoneNumber", async (phoneNumber) => {
      try {
        const validation = await twilioClient.lookups.v2
          .phoneNumbers(phoneNumber)
          .fetch();
        return validation;
      } catch (error) {
        fastify.log.error("Twilio phone number validation failed:", error);
        throw new Error("Failed to validate phone number.");
      }
    });

    console.info("   ✅ Twilio Enabled");
  }

  /*
    ===== CLOUDINARY =====
  */
  if (cloudinaryOptions.active !== false) {
    if (
      !cloudinaryOptions.cloudName ||
      !cloudinaryOptions.apiKey ||
      !cloudinaryOptions.apiSecret
    ) {
      throw new Error(
        "Cloudinary cloudName, apiKey, and apiSecret must be provided."
      );
    }

    Cloudinary.config({
      cloud_name: cloudinaryOptions.cloudName,
      api_key: cloudinaryOptions.apiKey,
      api_secret: cloudinaryOptions.apiSecret,
    },
    );

    fastify.decorate("cloudinaryUpload", async (fileStream, options = {}) => {
      return new Promise((resolve, reject) => {
        if (cloudinaryOptions.folder) {
          options.folder = options.folder ? options.folder : cloudinaryOptions.folder;
        }
        // console.log('options', options)
        options.resource_type = options.resource_type ? options.resource_type : 'auto';
        options.timestamp = Math.floor(Date.now() / 1000);  // Add timestamp to options
        options.use_filename = options.use_filename ? options.use_filename : true;
        options.unique_filename = options.unique_filename ? options.unique_filename : true;
        options
        const uploadStream = Cloudinary.uploader.upload_stream(options, (error, result) => {
          if (error) {
            // Log full error details for debugging
            fastify.log.error("Cloudinary upload failed:", error);
            reject(new Error(`Failed to upload to Cloudinary: ${JSON.stringify(error)}`));  // Return detailed error message
          } else {
            resolve(result);  // Resolve with the result from Cloudinary
          }
        });

        // Pipe the file stream to Cloudinary's upload stream
        fileStream.pipe(uploadStream)
          .on('error', (streamError) => {
            fastify.log.error("Stream error:", streamError);  // Log stream error
            reject(new Error(`Stream error during Cloudinary upload: ${streamError.message}`));
          })
          .on('end', () => {
            fastify.log.info("Stream ended successfully.");
          });
      });
    });
    fastify.decorate("cloudinaryUploadLarge", async (fileStream, options = {}) => {
      return new Promise((resolve, reject) => {
        if (cloudinaryOptions.folder) {
          options.folder = options.folder ? options.folder : cloudinaryOptions.folder;
        }
        // console.log('options', options)
        options.resource_type = options.resource_type ? options.resource_type : 'auto';
        options.timestamp = Math.floor(Date.now() / 1000);  // Add timestamp to options
        options.use_filename = options.use_filename ? options.use_filename : true;
        options.unique_filename = options.unique_filename ? options.unique_filename : true;
        options.overwrite = options.overwrite ? options.overwrite : false;

        options
        const uploadStream = Cloudinary.uploader.upload_stream(options, (error, result) => {
          if (error) {
            // Log full error details for debugging
            fastify.log.error("Cloudinary upload failed:", error);
            reject(new Error(`Failed to upload to Cloudinary: ${JSON.stringify(error)}`));  // Return detailed error message
          } else {
            resolve(result);  // Resolve with the result from Cloudinary
          }
        });

        // Pipe the file stream to Cloudinary's upload stream
        fileStream.pipe(uploadStream)
          .on('error', (streamError) => {
            fastify.log.error("Stream error:", streamError);  // Log stream error
            reject(new Error(`Stream error during Cloudinary upload: ${streamError.message}`));
          })
          .on('end', () => {
            fastify.log.info("Stream ended successfully.");
          });
      });
    });

    fastify.decorate("cloudinaryDelete", async (publicId) => {
      try {
        const response = await Cloudinary.uploader.destroy(publicId);
        return response;
      } catch (error) {
        fastify.log.error("Cloudinary delete failed:", error);
        throw new Error("Failed to delete from Cloudinary.");
      }
    });

    console.info("   ✅ Cloudinary Enabled");
  }

  /*
    ===== AUTHENTICATION =====
    Admin and User authentication are handled separately and redundantly,
    sharing no code between them.
  */

  // Register @fastify/cookie plugin
  fastify.register(import("@fastify/cookie"));

  console.info("   ✅ @fastify/cookie Enabled");

  /*
    ===== Admin Authentication =====
  */
  /*
  ===== Admin Authentication =====
*/
  if (authOptions.admin?.active !== false) {

    // Ensure the admin JWT secret is provided
    if (!authOptions.admin.secret) {
      throw new Error("Admin JWT secret must be provided.");
    }

    const adminAuthOptions = authOptions.admin;
    const adminCookieName =
      adminAuthOptions.cookieOptions?.name || "adminToken";
    const adminRefreshCookieName =
      adminAuthOptions.cookieOptions?.refreshTokenName || "adminRefreshToken";
    const adminCookieOptions = {
      httpOnly: true,  // Ensures the cookie is not accessible via JavaScript
      secure: isProduction,  // true in production (HTTPS), false in development (HTTP)
      sameSite: isProduction ? 'None' : 'Lax',  // 'None' for cross-origin, 'Lax' for development
      path: '/',  // Ensure cookies are valid for the entire site
    };
    const adminExcludedPaths = adminAuthOptions.excludedPaths || [
      "/admin/auth/login",
      "/admin/auth/logout",
    ];

    // Decorator to hash admin passwords
    async function hashAdminPassword(password) {
      const saltRounds = 10;  // Number of salt rounds for bcrypt (10 is generally a good default)
      try {
        const hashedPassword = await bcrypt.hash(password, saltRounds);
        return hashedPassword;
      } catch (error) {
        throw new Error("Failed to hash password: " + error.message);
      }
    }

    fastify.decorate("hashAdminPassword", hashAdminPassword);

    // Register JWT for admin
    await fastify.register(jwt, {
      secret: adminAuthOptions.secret,
      sign: { algorithm: 'HS256', expiresIn: adminAuthOptions.expiresIn || "15m" },
      cookie: {
        cookieName: adminCookieName,
        signed: false,
      },
      namespace: "adminJwt",
      jwtVerify: "adminJwtVerify",
      jwtSign: "adminJwtSign",
    });

    // Common function to set tokens as cookies
    const setAdminAuthCookies = (reply, accessToken, refreshToken) => {
      reply.setCookie(adminCookieName, accessToken, adminCookieOptions);
      reply.setCookie(adminRefreshCookieName, refreshToken, {
        // ...adminCookieOptions,
        httpOnly: true,  // Ensures the cookie is not accessible via JavaScript
        secure: isProduction,  // true in production (HTTPS), false in development (HTTP)
        sameSite: isProduction ? 'None' : 'Lax',  // 'None' for cross-origin, 'Lax' for development
        path: '/',  // Ensure cookies are valid for the entire site
      });
    };

    // Admin authentication hook
    fastify.addHook("onRequest", async (request, reply) => {
      const url = request.url;

      // Skip authentication for excluded paths
      if (adminExcludedPaths.some((path) => url.startsWith(path))) {
        return;
      }

      if (url.startsWith("/admin")) {
        try {
          // Extract token from cookie or Authorization header
          const authHeader = request.headers.authorization;
          const authToken =
            authHeader && authHeader.startsWith("Bearer ")
              ? authHeader.slice(7)
              : null;
          const token = request.cookies[adminCookieName] || authToken;

          if (!token) {
            throw fastify.httpErrors.unauthorized(
              "Admin access token not provided"
            );
          }

          // Verify access token
          const decoded = await request.adminJwtVerify(token);
          request.adminAuth = decoded; // Attach admin auth context
        } catch (err) {
          // Use built-in HTTP error handling
          reply.send(
            fastify.httpErrors.unauthorized("Invalid or expired access token")
          );
        }
      }
    });

    // Admin login route
    fastify.post("/admin/auth/login", async (req, reply) => {
      try {
        const { email, password } = req.body;

        // Validate input
        if (!email || !password) {
          throw fastify.httpErrors.badRequest(
            "Email and password are required"
          );
        }

        // Fetch admin from the database
        const admin = await fastify.prisma.admins.findUnique({
          where: { email },
        });
        if (!admin) {
          throw fastify.httpErrors.unauthorized("Invalid credentials");
        }

        // Compare passwords using bcrypt
        const isValidPassword = await bcrypt.compare(password, admin.password);
        if (!isValidPassword) {
          throw fastify.httpErrors.unauthorized("Invalid credentials");
        }

        // Issue access token
        const accessToken = await reply.adminJwtSign({ id: admin.id });

        // Generate refresh token
        const refreshToken = randomUUID();
        const hashedRefreshToken = await bcrypt.hash(refreshToken, 10);

        // Store hashed refresh token in the database
        await fastify.prisma.admins.update({
          where: { id: admin.id },
          data: { refreshToken: hashedRefreshToken },
        });

        // Set tokens as cookies
        setAdminAuthCookies(reply, accessToken, refreshToken);

        reply.send({ accessToken });
      } catch (err) {
        reply.send(err);
      }
    });

    // Admin refresh token route
    fastify.post("/admin/auth/refresh", async (req, reply) => {
      try {
        const adminAuth = req.adminAuth;
        const refreshToken = req.cookies[adminRefreshCookieName];
        if (!refreshToken) {
          throw fastify.httpErrors.unauthorized("Refresh token not provided");
        }

        // Fetch admin from the database using the refresh token
        const admin = await fastify.prisma.admins.findFirst({
          where: { id: adminAuth?.id, refreshToken: { not: null } },
        });
        if (!admin) {
          throw fastify.httpErrors.unauthorized("Invalid refresh token");
        }

        // Verify the refresh token
        const isValid = await bcrypt.compare(refreshToken, admin.refreshToken);
        if (!isValid) {
          throw fastify.httpErrors.unauthorized("Invalid refresh token");
        }

        // Issue new access token
        const accessToken = await reply.adminJwtSign({ id: admin.id });

        // Generate new refresh token
        const newRefreshToken = randomUUID();
        const hashedNewRefreshToken = await bcrypt.hash(newRefreshToken, 10);

        // Update refresh token in the database
        await fastify.prisma.admins.update({
          where: { id: admin.id },
          data: { refreshToken: hashedNewRefreshToken },
        });

        // Set new tokens as cookies
        setAdminAuthCookies(reply, accessToken, newRefreshToken);

        reply.send({ accessToken });
      } catch (err) {
        reply.send(err);
      }
    });

    // Admin logout route
    fastify.post("/admin/auth/logout", async (req, reply) => {
      try {
        const adminAuth = req.adminAuth;
        if (adminAuth) {
          // Delete refresh token from the database
          await fastify.prisma.admins.update({
            where: { id: adminAuth.id },
            data: { refreshToken: null },
          });
        }

        // Clear cookies
        reply.clearCookie(adminCookieName, { path: "/" });
        reply.clearCookie(adminRefreshCookieName, { path: "/" });

        reply.send({ message: "Logged out successfully" });
      } catch (err) {
        reply.send(err);
      }
    });

    // Admin authentication status route
    fastify.get("/admin/auth/me", async (req, reply) => {
      try {
        const adminAuth = req.adminAuth;

        // Fetch admin details from database
        const admin = await fastify.prisma.admins.findUnique({
          where: { id: adminAuth.id },
          select: { id: true, firstName: true, lastName: true, email: true },
        });

        if (!admin) {
          throw fastify.httpErrors.notFound("Admin not found");
        }

        reply.send(admin);
      } catch (err) {
        reply.send(err);
      }
    });

    console.info("   ✅ Auth Admin Enabled");
  }

  /*
  ===== User Authentication =====
*/
  if (authOptions.user?.active !== false) {
    // Ensure the user JWT secret is provided
    if (!authOptions.user.secret) {
      throw new Error("User JWT secret must be provided.");
    }

    const userAuthOptions = authOptions.user;
    const userCookieName = userAuthOptions.cookieOptions?.name || "userToken";
    const userRefreshCookieName =
      userAuthOptions.cookieOptions?.refreshTokenName || "userRefreshToken";
    const userCookieOptions = {
      httpOnly: true,  // Ensures the cookie is not accessible via JavaScript
      secure: isProduction,  // true in production (HTTPS), false in development (HTTP)
      sameSite: isProduction ? 'None' : 'Lax',  // 'None' for cross-origin, 'Lax' for development
      path: '/',  // Ensure cookies are valid for the entire site
    };
    const userExcludedPaths = userAuthOptions.excludedPaths || [
      "/portal/auth/login",
      "/portal/auth/logout",
      "/portal/auth/register",
    ];

    // Register JWT for user
    await fastify.register(jwt, {
      secret: userAuthOptions.secret,
      sign: { algorithm: 'HS256', expiresIn: userAuthOptions.expiresIn || "15m" },
      cookie: { cookieName: userCookieName, signed: false },
      namespace: "userJwt",
      jwtVerify: "userJwtVerify",
      jwtSign: "userJwtSign",
    });

    // Common function to set tokens as cookies
    const setAuthCookies = (reply, accessToken, refreshToken) => {
      reply.setCookie(userCookieName, accessToken, userCookieOptions);
      reply.setCookie(userRefreshCookieName, refreshToken, {
        // ...userCookieOptions,
        httpOnly: true,  // Ensures the cookie is not accessible via JavaScript
        secure: isProduction,  // true in production (HTTPS), false in development (HTTP)
        sameSite: isProduction ? 'None' : 'Lax',  // 'None' for cross-origin, 'Lax' for development
        path: '/',  // Ensure cookies are valid for the entire site
      });
    };

    // User authentication hook
    fastify.addHook("onRequest", async (request, reply) => {
      const url = request.url;

      // Skip authentication for excluded paths
      if (userExcludedPaths.some((path) => url.startsWith(path))) {
        return;
      }

      if (url.startsWith("/portal")) {
        try {
          // Extract token from cookie or Authorization header
          const authHeader = request.headers.authorization;
          const authToken =
            authHeader && authHeader.startsWith("Bearer ")
              ? authHeader.slice(7)
              : null;
          const token = request.cookies[userCookieName] || authToken;

          if (!token) {
            throw fastify.httpErrors.unauthorized(
              "User access token not provided"
            );
          }

          // Verify access token using the namespaced verify method
          const decoded = await request.userJwtVerify(token);
          request.userAuth = decoded; // Attach user auth context
        } catch (err) {
          // Use built-in HTTP error handling
          reply.send(
            fastify.httpErrors.unauthorized("Invalid or expired access token")
          );
        }
      }
    });

    // User registration route
    fastify.post("/portal/auth/register", async (req, reply) => {
      try {
        const { email, password, firstName, lastName } = req.body;

        // Validate input
        if (!email || !password || !firstName || !lastName) {
          throw fastify.httpErrors.badRequest("Missing required fields");
        }

        // Check if user already exists
        const existingUser = await fastify.prisma.users.findUnique({
          where: { email },
        });
        if (existingUser) {
          throw fastify.httpErrors.conflict("Email already in use");
        }

        // Hash the password
        const hashedPassword = await bcrypt.hash(password, 10);

        // Create the user
        const user = await fastify.prisma.users.create({
          data: {
            email,
            password: hashedPassword,
            firstName,
            lastName,
          },
        });

        // Issue access token
        const accessToken = await reply.userJwtSign({ id: user.id });

        // Generate refresh token
        const refreshToken = randomUUID();
        const hashedRefreshToken = await bcrypt.hash(refreshToken, 10);

        // Store hashed refresh token in the database
        await fastify.prisma.users.update({
          where: { id: user.id },
          data: { refreshToken: hashedRefreshToken },
        });

        // Set tokens as cookies
        setAuthCookies(reply, accessToken, refreshToken);

        reply.send({ accessToken });
      } catch (err) {
        reply.send(err);
      }
    });

    // User login route
    fastify.post("/portal/auth/login", async (req, reply) => {
      try {
        const { email, password } = req.body;

        if (!email || !password) {
          throw fastify.httpErrors.badRequest(
            "Email and password are required"
          );
        }

        // Fetch user from the database
        const user = await fastify.prisma.users.findUnique({
          where: { email },
        });
        if (!user) {
          throw fastify.httpErrors.unauthorized("Invalid credentials");
        }

        // Compare passwords using bcrypt
        const isValidPassword = await bcrypt.compare(password, user.password);
        if (!isValidPassword) {
          throw fastify.httpErrors.unauthorized("Invalid credentials");
        }

        // Issue access token
        const accessToken = await reply.userJwtSign({ id: user.id });

        // Generate refresh token
        const refreshToken = randomUUID();
        const hashedRefreshToken = await bcrypt.hash(refreshToken, 10);

        // Store hashed refresh token in the database
        await fastify.prisma.users.update({
          where: { id: user.id },
          data: { refreshToken: hashedRefreshToken },
        });

        // Set tokens as cookies
        setAuthCookies(reply, accessToken, refreshToken);

        reply.send({ accessToken });
      } catch (err) {
        reply.send(err);
      }
    });

    // User refresh token route
    fastify.post("/portal/auth/refresh", async (req, reply) => {
      try {
        const userAuth = req.userAuth;
        const refreshToken = req.cookies[userRefreshCookieName];
        if (!refreshToken) {
          throw fastify.httpErrors.unauthorized("Refresh token not provided");
        }

        // Fetch user from the database using the refresh token
        const user = await fastify.prisma.users.findFirst({
          where: { id: userAuth?.id, refreshToken: { not: null } },
        });
        if (!user) {
          throw fastify.httpErrors.unauthorized("Invalid refresh token");
        }

        // Verify the refresh token
        const isValid = await bcrypt.compare(refreshToken, user.refreshToken);
        if (!isValid) {
          throw fastify.httpErrors.unauthorized("Invalid refresh token");
        }

        // Issue new access token
        const accessToken = await reply.userJwtSign({ id: user.id });

        // Generate new refresh token
        const newRefreshToken = randomUUID();
        const hashedNewRefreshToken = await bcrypt.hash(newRefreshToken, 10);

        // Update refresh token in the database
        await fastify.prisma.users.update({
          where: { id: user.id },
          data: { refreshToken: hashedNewRefreshToken },
        });

        // Set new tokens as cookies
        setAuthCookies(reply, accessToken, newRefreshToken);

        reply.send({ accessToken });
      } catch (err) {
        reply.send(err);
      }
    });

    // User logout route
    fastify.post("/portal/auth/logout", async (req, reply) => {
      try {
        const userAuth = req.userAuth;
        if (userAuth) {
          // Delete refresh token from the database
          await fastify.prisma.users.update({
            where: { id: userAuth.id },
            data: { refreshToken: null },
          });
        }

        // Clear cookies
        reply.clearCookie(userCookieName, { path: "/" });
        reply.clearCookie(userRefreshCookieName, { path: "/" });

        reply.send({ message: "Logged out successfully" });
      } catch (err) {
        reply.send(err);
      }
    });

    // User authentication status route
    fastify.get("/portal/auth/me", async (req, reply) => {
      try {
        const userAuth = req.userAuth;

        // Fetch user details from database
        const user = await fastify.prisma.users.findUnique({
          where: { id: userAuth.id },
          select: { id: true, email: true, firstName: true, lastName: true },
        });

        if (!user) {
          throw fastify.httpErrors.notFound("User not found");
        }

        reply.send(user);
      } catch (err) {
        reply.send(err);
      }
    });

    console.info("   ✅ Auth User Enabled");
  }
  /*
     ===== LIST ROUTES AFTER ALL PLUGINS =====
     Use the after() method to ensure this runs after all plugins are registered.
   */
  fastify.after(() => {
    if (professional !== true) {
      console.info("   ✅ Listing Routes:");
      fastify.ready(() => {
        printRoutes(routes, options.colors !== false);
        // Add rocket emoji
        console.info(
          `🚀 Server is ready on port ${process.env.PORT || 3000}\n\n`
        );
        // Add goodbye emoji for server shutting down
        fastify.addHook("onClose", () =>
          console.info("Server shutting down... Goodbye 👋")
        );
      });
    }
  });

  /*
   * ===== HEALTH CHECK ROUTE =====
   */

  // Health Check Route
  fastify.get("/health", async (request, reply) => {
    const status = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      uptime: process.uptime(), // Uptime in seconds
      version: "1.0.0", // Fetch dynamically if possible
      environment: process.env.NODE_ENV || "development",
      dependencies: {},
      resources: {},
      details: {},
    };

    try {
      // Database connectivity check
      if (fastify.prisma) {
        await fastify.prisma.$queryRaw`SELECT 1`;
        status.dependencies.database = "up";
      } else {
        status.dependencies.database = "not configured";
      }
    } catch (error) {
      status.dependencies.database = "down";
      status.status = "degraded";
      status.details.databaseError = error.message;
    }

    try {
      // Redis connectivity check
      if (fastify.redis) {
        await fastify.redis.ping();
        status.dependencies.redis = "up";
      } else {
        status.dependencies.redis = "not configured";
      }
    } catch (error) {
      status.dependencies.redis = "down";
      status.status = "degraded";
      status.details.redisError = error.message;
    }

    // Resource usage
    const memoryUsage = process.memoryUsage();
    const loadAverage = os.loadavg();

    status.resources.memory = {
      rss: formatBytes(memoryUsage.rss),
      heapTotal: formatBytes(memoryUsage.heapTotal),
      heapUsed: formatBytes(memoryUsage.heapUsed),
      external: formatBytes(memoryUsage.external),
      arrayBuffers: formatBytes(memoryUsage.arrayBuffers),
    };

    status.resources.cpu = {
      loadAverage, // 1, 5, and 15 minute load averages
      cpus: os.cpus().length,
    };

    // Disk space availability
    try {
      // Implement disk space check using 'check-disk-space' package
      // Install it via 'npm install check-disk-space'
      const checkDiskSpace = (await import("check-disk-space")).default;
      const diskSpace = await checkDiskSpace("/"); // Replace '/' with your drive or mount point
      status.resources.disk = {
        free: formatBytes(diskSpace.free),
        size: formatBytes(diskSpace.size),
      };
    } catch (error) {
      status.resources.disk = "unknown";
      status.details.diskError = error.message;
    }

    // Application-specific checks
    // Example: Check if a scheduled job is running
    if (typeof isJobRunning === "function" && !isJobRunning()) {
      status.status = "degraded";
      status.details.jobStatus = "Scheduled job is not running";
    }

    // Configuration validation
    const requiredEnvVars = [
      "DATABASE_URL",
      "ADMIN_JWT_SECRET",
      "USER_JWT_SECRET",
    ];
    const missingEnvVars = requiredEnvVars.filter(
      (varName) => !process.env[varName]
    );
    if (missingEnvVars.length > 0) {
      status.status = "degraded";
      status.details.missingEnvVars = missingEnvVars;
    }

    // Determine overall health
    if (status.status === "healthy") {
      reply.send(status);
    } else {
      reply.status(500).send(status);
    }
  });

  /*
    ===== Slugify =====
  */
  fastify.decorate('slugify', (string) => {
    return string
      .toString()
      .toLowerCase()                   // Convert to lowercase
      .trim()                          // Trim leading and trailing spaces
      .replace(/\s+/g, '-')            // Replace spaces with -
      .replace(/[^\w\-]+/g, '')        // Remove all non-word characters (allow only letters, numbers, and dashes)
      .replace(/\-\-+/g, '-')          // Replace multiple dashes with a single dash
      .replace(/^-+/, '')              // Trim leading dashes
      .replace(/-+$/, '');             // Trim trailing dashes
  });
  /*
    ===== Generate UUID =====
  */
  fastify.decorate('UUID', () => {
    return randomUUID(); // Generate a UUID using uncrypto's randomUUID
  });

  /*
    ===== Ensure Proper Cleanup on Server Shutdown =====
  */
  fastify.addHook("onClose", async () => {
    if (fastify.prisma) {
      await fastify.prisma.$disconnect();
    }
    // Add additional cleanup for other services if necessary
  });
}

export default fp(xConfig, {
  name: "xConfig",
});
