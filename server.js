import Fastify from "fastify";
import app from "./app.js";

const fastify = Fastify({
  logger: {
    level: "info",
  },
  bodyLimit: 50 * 1024 * 1024, // 50MB body limit
  maxParamLength: 500,
  requestTimeout: 300000, // 5 minutes timeout for large uploads
  keepAliveTimeout: 300000, // 5 minutes keep alive
  connectionTimeout: 300000, // 5 minutes connection timeout
  ignoreTrailingSlash: true,
  ignoreDuplicateSlashes: true,
});

// Register the app
fastify.register(app);

// Start the server
const start = async () => {
  try {
    await fastify.listen({ port: 3001, host: "0.0.0.0" });
    console.log("🚀 Server is ready on port 3001");
  } catch (err) {
    fastify.log.error(err);
    process.exit(1);
  }
};

start();
