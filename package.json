{"type": "module", "name": "api-shaw<PERSON>gan", "description": "This project was bootstrapped with Fastify-CLI.", "version": "1.0.0", "main": "app.js", "engines": {"node": ">=20.0.0"}, "directories": {"test": "test"}, "scripts": {"test": "node --test test/**/*.test.js", "start": "node server.js", "dev": "node --watch server.js", "dev:fastify": "fastify start -w -l info -P app.js", "postinstall": "prisma generate"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@fastify/autoload": "^6.0.1", "@fastify/multipart": "^8.3.0", "@xenterprises/fastify-xconfig": "^1.1.7", "fastify": "^4.28.1", "fastify-cli": "^6.2.1", "fastify-cloudinary": "^2.0.0", "fastify-multipart": "^5.4.0", "fastify-plugin": "^4.0.0", "twilio": "^5.3.2", "uncrypto": "^0.1.3"}, "devDependencies": {"c8": "^9.0.0", "prisma": "^6.8.2"}}