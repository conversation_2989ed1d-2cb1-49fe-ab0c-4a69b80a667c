export default async function (fastify, opts) {
  fastify.put('/', async (req, reply) => {
    const { programId } = req.params;
    const { body } = req;
    delete body.id;
    const response = await fastify.prisma.programs.update({
      where: {
        id: programId,
      },
      data: {
        ...body
      }
    });

    const [key, value] = Object.entries(req.body)[0];
    const log = await fastify.prisma.programUpdateLog.create({
      data: {
        programId: programId,
        adminId: req.adminAuth?.id,
        key: key,
        value: value
      }
    })
    reply.send(response);
  });
}