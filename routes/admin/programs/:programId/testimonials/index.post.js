export default async function (fastify, opts) {
  fastify.post('/', async (req, reply) => {
    const { programId } = req.params;
    const { body } = req;
    const length = await fastify.prisma.programTestimonials.findMany({
      where: {
        programsId: programId,
      },
      select: {
        id: true,
      },
    });
    const response = await fastify.prisma.programTestimonials.create({
      data: {
        programsId: programId,
        order: length.length + 1,
        ...body,
      },
    });
    reply.send(response);
  });
}