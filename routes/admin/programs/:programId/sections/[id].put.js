export default async function (fastify, opts) {
  fastify.put('/:id', async (req, reply) => {
    const { programId, id } = req.params;
    const { body } = req;
    let photo = {};

    if (req.file) {
      const data = await req.file();

      // Upload the file to Cloudinary
      const { secure_url, format, height, width, public_id } = await fastify.cloudinaryUpload(data.file, {
        // public_id: `programs/${req.params.programId}/photos/${data.filename}`,
        folder: `programs/${req.params.programId}/photos`,
      });
      photo.photo = secure_url;
      photo.public_id = public_id
    }




    const response = await fastify.prisma.programSections.update({
      where: {
        id: id,
      },
      data: {
        ...body,
        ...photo
      }
    });
    reply.send(response);
  });
  fastify.put('/:id/', async (req, reply) => {
    const { programId, id } = req.params;
    const { body } = req;

    const response = await fastify.prisma.programSections.update({
      where: {
        id: id,
      },
      data: {
        ...body,
      }
    });
    reply.send(response);
  });
}