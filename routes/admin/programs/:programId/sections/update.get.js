export default async function (fastify, opts) {
  fastify.get('/update/:id', async (req, reply) => {
    const { programId, id } = req.params;

    const response = await fastify.prisma.programSections.findUnique({
      where: {
        id: id,
      },
    });
    reply.send(response);
  });
  fastify.get('/update/', async (req, reply) => {
    const { programId } = req.params;
    const response = await fastify.prisma.programSections.findMany({
      where: {
        programsId: programId,
      },
    });
    reply.send(response);
  });
}