export default async function (fastify, opts) {
  fastify.post('/', async (req, reply) => {
    const { programId } = req.params;
    const { body } = req;
    const length = await fastify.prisma.programSections.findMany({
      where: {
        programsId: programId,
      },
      select: {
        id: true,
      },
    });
    const response = await fastify.prisma.programSections.create({
      data: {
        programsId: programId,
        title: body.title,
        body: body.body,
        order: length.length + 1,
      },
    });
    reply.send(response);
  });
}