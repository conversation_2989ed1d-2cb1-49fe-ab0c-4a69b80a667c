export default async function (fastify, opts) {
  fastify.post("/", async (req, reply) => {
    // Get the file from the multipart request
    const data = await req.file();

    // Upload the file to Cloudinary
    const { secure_url, format, height, width, public_id } =
      await fastify.cloudinaryUpload(data.file, {
        // public_id: `programs/${req.params.programId}/photos/${data.filename}`,
        folder: `programs/${req.params.programId}/videos`,
      });

    // Create a new record in the database
    const response = await fastify.prisma.programsVideos.create({
      data: {
        programsId: req.params.programId,
        public_id: public_id,
        url: secure_url,
      },
    });

    reply.send(response);
  });
}
