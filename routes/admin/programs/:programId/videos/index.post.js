export default async function (fastify, opts) {
  fastify.post("/", async (req, reply) => {
    try {
      // Get the file from the multipart request
      const data = await req.file();

      if (!data) {
        return reply.code(400).send({ error: "No file provided" });
      }

      // Check file size
      if (data.file.bytesRead > 50 * 1024 * 1024) {
        return reply
          .code(413)
          .send({ error: "File too large. Maximum size is 50MB." });
      }

      // Upload the file to Cloudinary
      const { secure_url, public_id } = await fastify.cloudinaryUpload(
        data.file,
        {
          folder: `programs/${req.params.programId}/videos`,
          resource_type: "video", // Explicitly set resource type for videos
        }
      );

      // Create a new record in the database
      const response = await fastify.prisma.programsVideos.create({
        data: {
          programsId: req.params.programId,
          public_id: public_id,
          url: secure_url,
        },
      });

      reply.send(response);
    } catch (error) {
      fastify.log.error("Video upload error:", error);

      if (
        error.message.includes("Request body size did not match Content-Length")
      ) {
        return reply.code(400).send({
          error:
            "Upload interrupted. Please try again with a stable connection.",
        });
      }

      if (error.message.includes("Request body is too large")) {
        return reply.code(413).send({
          error: "File too large. Maximum size is 50MB.",
        });
      }

      return reply.code(500).send({
        error: "Upload failed. Please try again.",
      });
    }
  });
}
