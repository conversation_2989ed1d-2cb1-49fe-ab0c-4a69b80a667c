export default async function (fastify, opts) {
  fastify.post(
    "/",
    {
      bodyLimit: 50 * 1024 * 1024, // 50MB body limit for this route
      preHandler: async (request, reply) => {
        // Set body limit for this specific request
        request.raw.bodyLimit = 50 * 1024 * 1024;
      },
    },
    async (req, reply) => {
      // Get the file from the multipart request
      const data = await req.file();

      // Upload the file to Cloudinary
      const { secure_url, format, height, width, public_id } =
        await fastify.cloudinaryUpload(data.file, {
          // public_id: `programs/${req.params.programId}/photos/${data.filename}`,
          folder: `programs/${req.params.programId}/videos`,
        });

      // Create a new record in the database
      const response = await fastify.prisma.programsVideos.create({
        data: {
          programsId: req.params.programId,
          public_id: public_id,
          url: secure_url,
        },
      });

      reply.send(response);
    }
  );
}
