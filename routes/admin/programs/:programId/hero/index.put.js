export default async function (fastify, opts) {
  fastify.put('/', async (req, reply) => {
    const { programId } = req.params;
    const { body } = req;
    let photo = {};

    if (req.file) {
      const data = await req.file();

      // Upload the file to Cloudinary
      const { secure_url, format, height, width, public_id } = await fastify.cloudinaryUpload(data.file, {
        // public_id: `programs/${req.params.programId}/photos/${data.filename}`,
        folder: `programs/${req.params.programId}/photos`,
      });
      photo.heroPhoto = secure_url;
      // photo.public_id = public_id
    }

    const response = await fastify.prisma.programs.update({
      where: {
        id: programId,
      },
      data: {
        ...photo
      }
    });
    reply.send(response);
  });
}