export default async function (fastify, opts) {
  fastify.get('/', async (req, reply) => {
    const response = await fastify.prisma.programs.findMany({
      where: {
        deleted: false
      },
      select: {
        id: true,
        name: true,
        slug: true,
        summary: true,
        icon: true,
      },
      orderBy: {
        name: 'asc'
      }
    });
    reply.send(response);
  });
}
