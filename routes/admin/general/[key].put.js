export default async function (fastify, opts) {

  fastify.put('/:key', async (req, reply) => {
    const response = await fastify.prisma.general.update({
      where: {
        key: req.params.key,
      },
      data: {
        value: req.body
      },
    });
    reply.send(response);
  });
  fastify.put('/:key/', async (req, reply) => {
    const response = await fastify.prisma.general.update({
      where: {
        key: req.params.key,
      },
      data: {
        value: req.body
      },
    });
    reply.send(response);
  });
}