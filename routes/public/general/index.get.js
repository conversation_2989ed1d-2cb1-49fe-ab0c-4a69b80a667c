export default async function (fastify, opts) {
  fastify.get('/', async (req, reply) => {
    const { slug } = req.params;
    const response = await fastify.prisma.general.findUnique({
      where: {
        key: "homepage"
      },
    });
    reply.send(response?.value);
  });
  fastify.get('/:key', async (req, reply) => {

    const response = await fastify.prisma.general.findUnique({
      where: {
        key: req.params.key,
      },
      select: {
        value: true
      }
    });
    reply.send(response?.value);
  });
}