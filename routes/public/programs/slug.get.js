export default async function (fastify, opts) {
  fastify.get('/slug/:slug', async (req, reply) => {
    const { slug } = req.params;
    const response = await fastify.prisma.programs.findFirst({
      where: {
        slug: slug,
      },
      include: {
        ProgramSections: true,
        ProgramsPhotos: true,
        ProgramTestimonials: true,
      },
    });
    reply.send(response);
  });


}
