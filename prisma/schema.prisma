generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["driverAdapters", "omitApi"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model General {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @default(now()) @updatedAt
  deleted   Boolean   @default(false)
  deletedAt DateTime?
  key       String    @unique
  value     Json?
}

model Faqs {
  id          String    @id @default(cuid())
  label       String
  content     String?
  createdAt   DateTime  @default(now())
  deleted     Boolean   @default(false)
  deletedAt   DateTime?
  updatedAt   DateTime  @default(now()) @updatedAt
  defaultOpen Boolean   @default(false)
}

model Programs {
  id                  String                @id @default(cuid())
  slug                String?
  headerVideo         String?
  description         String?
  careers             String?
  skills              Json?
  lifeSkills          Json?
  problem             String?
  refinement          String?
  audience            String?
  icon                String?
  featuresTitle       String?
  name                String
  active              Boolean               @default(false)
  headerVideos        Json?
  summary             String?
  altVideo            String?
  heroPhoto           String?
  altPhoto            String?
  createdAt           DateTime              @default(now())
  deleted             Boolean               @default(false)
  deletedAt           DateTime?
  updatedAt           DateTime              @default(now()) @updatedAt
  featuredVideo       String?
  isComingSoon        Boolean               @default(false)
  externalLink        String?
  problemHeader       String?
  sectionFeaturePhoto String?
  refinementHeader    String?
  careersHeader       String?
  heroName            String?
  careersBody         String?
  heroDescription     String?
  skillsBody          String?
  skillsHeader        String?
  audienceHeader      String?
  lifeSkillsBody      String?
  lifeSkillsHeader    String?
  navDescription      String?
  problemPhoto        String?
  ProgramSections     ProgramSections[]
  ProgramTestimonials ProgramTestimonials[]
  ProgramUpdateLog    ProgramUpdateLog[]
  ProgramsPhotos      ProgramsPhotos[]
  ProgramsVideos      ProgramsVideos[]
}

model ProgramTestimonials {
  id         String    @id @default(cuid())
  name       String?
  title      String?
  quote      String?
  order      Int
  programsId String?
  createdAt  DateTime  @default(now())
  deleted    Boolean   @default(false)
  deletedAt  DateTime?
  updatedAt  DateTime  @default(now()) @updatedAt
  Programs   Programs? @relation(fields: [programsId], references: [id])
}

model ProgramsPhotos {
  id         String    @id @default(cuid())
  url        String
  programsId String
  createdAt  DateTime  @default(now())
  deleted    Boolean   @default(false)
  deletedAt  DateTime?
  updatedAt  DateTime  @default(now()) @updatedAt
  name       String?
  public_id  String?
  Programs   Programs  @relation(fields: [programsId], references: [id])
}

model ProgramsVideos {
  id         String    @id @default(cuid())
  url        String
  programsId String
  createdAt  DateTime  @default(now())
  deleted    Boolean   @default(false)
  deletedAt  DateTime?
  updatedAt  DateTime  @default(now()) @updatedAt
  name       String?
  public_id  String?
  Programs   Programs  @relation(fields: [programsId], references: [id])
}

model ProgramSections {
  id         String    @id @default(cuid())
  title      String
  body       String?
  photo      String?
  order      Int
  programsId String
  createdAt  DateTime  @default(now())
  deleted    Boolean   @default(false)
  deletedAt  DateTime?
  updatedAt  DateTime  @default(now()) @updatedAt
  public_id  String?
  Programs   Programs  @relation(fields: [programsId], references: [id])
}

model Users {
  id           String    @id @default(cuid())
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @default(now()) @updatedAt
  deleted      Boolean   @default(false)
  deletedAt    DateTime?
  firstName    String?
  lastName     String?
  email        String    @unique
  sms          String?
  password     String
  refreshToken String?
}

model Admins {
  id               String             @id @default(cuid())
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @default(now()) @updatedAt
  deleted          Boolean            @default(false)
  deletedAt        DateTime?
  isActive         Boolean            @default(true)
  firstName        String?
  lastName         String?
  email            String             @unique
  sms              String?
  password         String
  refreshToken     String?
  ProgramUpdateLog ProgramUpdateLog[]
}

model ProgramUpdateLog {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @default(now()) @updatedAt
  deleted   Boolean   @default(false)
  deletedAt DateTime?
  key       String?
  value     String?
  adminId   String?
  programId String?
  Admins    Admins?   @relation(fields: [adminId], references: [id])
  Programs  Programs? @relation(fields: [programId], references: [id])
}
